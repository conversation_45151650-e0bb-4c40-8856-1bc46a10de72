using Atlas.Application.Interfaces;
using Atlas.Application.Models.MeetingMinute;
using Atlas.CrossCutting.Models.Responses;
using Atlas.Data.Enums;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Atlas.WebApi.Controllers
{
    /// <summary>
    /// Meeting Minute Controller
    /// </summary>
    [ApiController]
    [Route("api/{clientId}/{workgroupId}/meeting/{parentContentUuId}/minute")]
    [Authorize]
    public class MeetingMinuteController : ControllerBase
    {
        private readonly IMeetingMinuteApplication _meetingMinuteApplication;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="meetingMinuteApplication">Meeting minute application service</param>
        public MeetingMinuteController(IMeetingMinuteApplication meetingMinuteApplication)
        {
            _meetingMinuteApplication = meetingMinuteApplication;
        }

        /// <summary>
        /// Creates a new meeting minute
        /// </summary>
        /// <param name="clientId">The client ID</param>
        /// <param name="workgroupId">The workgroup ID</param>
        /// <param name="parentContentUuId">The meeting UUID</param>
        /// <param name="request">The minute creation request</param>
        /// <returns>The created meeting minute response</returns>
        [HttpPost]
        [Authorize(Policy = PolicyConstants.ParentContentOwnerValidation)]
        public async Task<ActionResult<ApiResponse<Guid>>> CreateMinute(
            [FromRoute] int clientId,
            [FromRoute] int workgroupId,
            [FromRoute] Guid parentContentUuId,
            [FromBody] MeetingMinuteCreateRequest request)
        {
            request.clientId = clientId;
            request.workgroupId = workgroupId;
            request.meetingUuid = parentContentUuId;

            var result = await _meetingMinuteApplication.CreateAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// Gets a meeting minute by content UUID
        /// </summary>
        /// <param name="contentUuId">The content UUID</param>
        /// <returns>The meeting minute response</returns>
        [HttpGet("{contentUuId}")]
        [Authorize(Policy = PolicyConstants.ContentPermissionValidation)]
        public async Task<ActionResult<ApiResponse<MeetingMinuteGetResponse>>> GetMeetingMinute([FromRoute] Guid contentUuId)
        {
            var result = await _meetingMinuteApplication.GetMeetingMinuteAsync(contentUuId);
            return Ok(result);
        }

        /// <summary>
        /// Generates HTML for meeting minute
        /// </summary>
        /// <param name="parentContentUuId">Meeting content UUID</param>
        /// <param name="language">Language for generation (pt, en, es)</param>
        /// <param name="includeActions">Include actions in HTML</param>
        /// <returns>Generated HTML string</returns>
        [HttpGet("generate-html")]
        [Authorize(Policy = PolicyConstants.ParentContentOwnerValidation)]
        public async Task<IActionResult> GenerateHtml(
            Guid parentContentUuId,
            [FromQuery] string language = "pt",
            [FromQuery] bool includeActions = true)
        {
            var html = await _meetingMinuteApplication.GenerateHtmlAsync(parentContentUuId, language, includeActions);
            return Content(html, "text/html");
        }
    }
}