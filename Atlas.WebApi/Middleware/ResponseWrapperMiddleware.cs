using Atlas.CrossCutting.Exceptions;
using Atlas.CrossCutting.Models.Responses;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System.Net;

namespace Atlas.WebApi.Middleware
{
    /// <summary>
    /// Middleware that wraps all API responses in a standardized format
    /// </summary>
    public class ResponseWrapperMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly JsonSerializerSettings _jsonSettings;

        /// <summary>
        /// Initializes a new instance of the ResponseWrapperMiddleware
        /// </summary>
        /// <param name="next">The next middleware in the pipeline</param>
        /// <param name="mvcOptions">The MVC Newtonsoft JSON options</param>
        public ResponseWrapperMiddleware(RequestDelegate next, IOptions<MvcNewtonsoftJsonOptions> mvcOptions)
        {
            _next = next ?? throw new ArgumentNullException(nameof(next));
            _jsonSettings = mvcOptions?.Value?.SerializerSettings ?? new JsonSerializerSettings
            {
                ContractResolver = new Newtonsoft.Json.Serialization.CamelCasePropertyNamesContractResolver(),
                NullValueHandling = NullValueHandling.Ignore,
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore
            };
        }

        /// <summary>
        /// Processes an HTTP request to wrap its response in a standardized format
        /// </summary>
        /// <param name="context">The HTTP context</param>
        public async Task InvokeAsync(HttpContext context)
        {
            if (context == null)
            {
                throw new ArgumentNullException(nameof(context));
            }

            if (context.Request.Path.StartsWithSegments("/healthy", StringComparison.OrdinalIgnoreCase))
            {
                await _next(context);
                return;
            }

            var originalBodyStream = context.Response.Body;
            using var memoryStream = new MemoryStream();
            context.Response.Body = memoryStream;

            try
            {
                await _next(context);

                var statusCode = context.Response.StatusCode;
                
                // Handle 204 No Content responses - they should not have a body or Content-Length header
                if (statusCode == 204)
                {
                    // Don't wrap 204 responses, just pass them through
                    return;
                }

                memoryStream.Seek(0, SeekOrigin.Begin);

                var contentType = context.Response.ContentType?.ToLower() ?? "";

                if (contentType.Contains("application/octet-stream") ||
                    contentType.Contains("application/zip") ||
                    contentType.Contains("application/vnd.openxmlformats") ||
                    contentType.Contains("application/vnd.ms-excel") ||
                    contentType.Contains("application/pdf") ||
                    contentType.Contains("image/") ||
                    contentType.Contains("text/csv") ||
                    contentType.Contains("text/html"))
                {
                    memoryStream.Seek(0, SeekOrigin.Begin);
                    await memoryStream.CopyToAsync(originalBodyStream);
                    return;
                }

                var responseBody = await new StreamReader(memoryStream).ReadToEndAsync();

                if (!string.IsNullOrEmpty(responseBody) &&
                    (responseBody.StartsWith("PK") ||
                     responseBody.StartsWith("%PDF")))
                {
                    memoryStream.Seek(0, SeekOrigin.Begin);
                    await memoryStream.CopyToAsync(originalBodyStream);
                    return;
                }

                object responseObject;
                if (string.IsNullOrEmpty(responseBody))
                {
                    responseObject = new ApiResponse<object>(
                        statusCode < 400,
                        statusCode < 400 ? "Operation completed successfully" : "An error occurred",
                        new { }
                    );
                }
                else
                {
                    try
                    {
                        var data = JsonConvert.DeserializeObject<object>(responseBody, _jsonSettings) ?? "";
                        responseObject = new ApiResponse<object>(
                            statusCode < 400,
                            statusCode < 400 ? "Operation completed successfully" : "An error occurred",
                            data
                        );
                    }
                    catch (Exception)
                    {
                        responseObject = new ApiResponse<string>(
                            statusCode < 400,
                            statusCode < 400 ? "Operation completed successfully" : "An error occurred",
                            responseBody
                        );
                    }
                }

                var wrappedBody = JsonConvert.SerializeObject(responseObject, _jsonSettings);
                var wrappedBytes = System.Text.Encoding.UTF8.GetBytes(wrappedBody);

                // Only set Content-Length for non-204 responses
                context.Response.Headers["Content-Length"] = wrappedBytes.Length.ToString();

                using var originalStream = new MemoryStream();
                await originalStream.WriteAsync(wrappedBytes);
                originalStream.Seek(0, SeekOrigin.Begin);
                await originalStream.CopyToAsync(originalBodyStream);
            }
            catch (FeatureFlagUnauthorizedException ex)
            {
                context.Response.StatusCode = (int)ex.StatusCode;
                
                // Criar resposta específica para FeatureFlag com a key
                var data = new { key = ex.FeatureKey };
                var response = new ApiResponse<object>(false, ex.Message, data);
                await WriteResponseAsync(context, response, originalBodyStream);
            }
            catch (HttpCustomException ex)
            {
                context.Response.StatusCode = (int)ex.StatusCode;
                var response = new ApiResponse<object>(false, ex.Message, new { });
                await WriteResponseAsync(context, response, originalBodyStream);
            }
            catch (Exception ex)
            {
                context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                var response = new ApiResponse<object>(false, "An unexpected error occurred", new { });
                await WriteResponseAsync(context, response, originalBodyStream);
            }
        }

        private async Task WriteResponseAsync<T>(HttpContext context, ApiResponse<T> response, Stream originalBodyStream)
        {
            var serializedResponse = JsonConvert.SerializeObject(response, _jsonSettings);
            var responseBytes = System.Text.Encoding.UTF8.GetBytes(serializedResponse);

            // Only set Content-Length for non-204 responses
            if (context.Response.StatusCode != 204)
            {
                context.Response.Headers["Content-Length"] = responseBytes.Length.ToString();
            }

            await originalBodyStream.WriteAsync(responseBytes);
        }
    }
}
