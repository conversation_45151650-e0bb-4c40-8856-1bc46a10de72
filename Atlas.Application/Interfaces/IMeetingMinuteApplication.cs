using Atlas.Application.Models.MeetingMinute;
using Atlas.CrossCutting.Models.Responses;

namespace Atlas.Application.Interfaces
{
    /// <summary>
    /// Interface for meeting minute application services
    /// </summary>
    public interface IMeetingMinuteApplication
    {
        /// <summary>
        /// Creates a new meeting minute
        /// </summary>
        /// <param name="request">The meeting minute creation request</param>
        /// <returns>The created meeting minute response</returns>
        Task<Guid> CreateAsync(MeetingMinuteCreateRequest request);
        Task<MeetingMinuteGetResponse> GetMeetingMinuteAsync(Guid contentUuId);
        
        /// <summary>
        /// Generates HTML for meeting minute
        /// </summary>
        /// <param name="request">Request with meeting details</param>
        /// <returns>Meeting minute HTML generation response</returns>
        Task<ApiResponse<MeetingMinuteHtmlGenerateResponse>> GenerateHtmlAsync(MeetingMinuteHtmlGenerateRequest request);
    }
}
