using Atlas.Application.Interfaces;
using Atlas.Application.Models.MeetingMinute;
using Atlas.CrossCutting.Exceptions;
using Atlas.CrossCutting.Logging;
using Atlas.CrossCutting.Services;
using MapsterMapper;
using System.Net;
using System.Security;
using System.Security.Principal;
using Atlas.Business.Core.MeetingMinute;
using Atlas.CrossCutting.DTO.MeetingMinute;
using Atlas.CrossCutting.Models.Responses;

namespace Atlas.Application.Handlers
{
    /// <summary>
    /// Implementation of meeting minute application services
    /// </summary>
    internal class MeetingMinuteApplication
        (
            IPrincipal principal,
            IStructuredLogger logger,
            IMapper mapper,
            IRequestContextService requestContext,
            IMeetingMinuteService meetingMinuteService
        ) : IMeetingMinuteApplication
    {
        private readonly IStructuredLogger _logger = logger;
        private readonly IMapper _mapper = mapper;
        private readonly IRequestContextService _requestContext = requestContext;
        private readonly IMeetingMinuteService _meetingMinuteService = meetingMinuteService;
        private readonly Atlas.CrossCutting.Helpers.AuthUtil _authUtil = new Atlas.CrossCutting.Helpers.AuthUtil(principal);

        /// <summary>
        /// Creates a new meeting minute
        /// </summary>
        /// <param name="request">The meeting minute creation request</param>
        /// <returns>The created meeting minute response</returns>
        public async Task<Guid> CreateAsync(MeetingMinuteCreateRequest request)
        {
            try
            {
                var dto = _mapper.Map<MeetingMinuteCreateDto>(request);
                var result = await _meetingMinuteService.CreateMeetingMinute(dto, requestContext.UserAgent);

                if (result == Guid.Empty)
                {
                    throw new HttpCustomException("Meeting minute creation failed.", HttpStatusCode.UnprocessableEntity);
                }

                return result;
            }
            catch (SecurityException ex)
            {
                _logger.LogError("Security exception during meeting minute creation.", ex);
                throw new HttpCustomException(ex.Message, HttpStatusCode.Forbidden);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError("Invalid operation during meeting minute creation.", ex);
                throw new HttpCustomException(ex.Message, HttpStatusCode.BadRequest);
            }
            catch (ArgumentException ex)
            {
                _logger.LogError("Argument exception during meeting minute creation.", ex);
                throw new HttpCustomException(ex.Message, HttpStatusCode.BadRequest);
            }
            catch (Exception ex)
            {
                _logger.LogError("Unexpected error during meeting minute creation.", ex);
                throw new HttpCustomException("An unexpected error occurred during meeting minute creation.", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<MeetingMinuteGetResponse> GetMeetingMinuteAsync(Guid contentUuId)
        {
            if (contentUuId == Guid.Empty)
                throw new HttpCustomException("Content UUID cannot be empty.", HttpStatusCode.BadRequest);

            var result = await _meetingMinuteService.GetMeetingMinute(contentUuId)
                ?? throw new HttpCustomException("MEETING_MINUTE_NOT_FOUND", HttpStatusCode.NotFound);

            var meetingMinute = result.MeetingMinute.FirstOrDefault()
                ?? throw new HttpCustomException("MEETING_MINUTE_NOT_FOUND", HttpStatusCode.NotFound);

            var response = _mapper.Map(meetingMinute, new MeetingMinuteGetResponse());

            response.UAC = await _meetingMinuteService.GetMeetingMinuteUACAsync(result);

            return response;
        }

        public async Task<string> GenerateHtmlAsync(Guid meetingContentUuid, string language = "pt", bool includeActions = true)
        {
            try
            {
                var contentRepository = new Atlas.Data.Repository.ContentRepository(_authUtil.UserId);
                var meetingContent = await contentRepository.GetSimpleContent(meetingContentUuid, includeRelatedEntities: true, isMeeting: true);

                if (meetingContent == null)
                    throw new HttpCustomException("MEETING_NOT_FOUND", HttpStatusCode.NotFound);

                if (meetingContent.type != "Meeting")
                    throw new HttpCustomException("CONTENT_IS_NOT_MEETING", HttpStatusCode.BadRequest);

                var htmlGenerator = new Atlas.Business.MeetingMinuteService(language, includeActions, meetingContent);

                var meetingDate = meetingContent.Meeting.FirstOrDefault()?.date ?? DateTime.UtcNow;
                var subscribers = meetingContent.ContentSubscriber?.ToList() ?? new List<Atlas.Data.Entities.ContentSubscriber>();

                var header = htmlGenerator.buildHeader(meetingDate);
                var description = htmlGenerator.buildDescription(meetingDate, subscribers);
                var agendaItems = htmlGenerator.buildAgendaItems();
                var polls = htmlGenerator.buildPoll(_authUtil.UserId);
                var tasks = htmlGenerator.buildTasks(_authUtil.UserId);

                // Combine all sections
                var fullHtml = $@"<!DOCTYPE html>
                                    <html>
                                    <head>
                                        <meta charset='utf-8'>
                                        <title>Meeting Minute</title>
                                        <style>
                                            body {{
                                                font-family: Arial, sans-serif;
                                                margin: 20px;
                                                line-height: 1.6;
                                                color: #333;
                                            }}
                                            h3 {{
                                                text-align: center;
                                                margin-bottom: 20px;
                                                color: #2c3e50;
                                            }}
                                            p {{
                                                margin: 10px 0;
                                            }}
                                            ul, ol {{
                                                margin: 10px 0;
                                                padding-left: 20px;
                                            }}
                                            li {{
                                                margin-bottom: 5px;
                                            }}
                                            b {{
                                                color: #2c3e50;
                                            }}
                                            .section {{
                                                margin-bottom: 20px;
                                            }}
                                        </style>
                                    </head>
                                    <body>
                                        <div class='section'>{header}</div>
                                        <div class='section'>{description}</div>
                                        <div class='section'>{agendaItems}</div>
                                        <div class='section'>{polls}</div>
                                        <div class='section'>{tasks}</div>
                                    </body>
                                    </html>";

                return fullHtml;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error generating meeting minute HTML.", ex);
                throw new HttpCustomException("Error generating meeting minute HTML.", HttpStatusCode.InternalServerError);
            }
        }

        public async Task<ApiResponse<MeetingMinuteHtmlGenerateResponse>> GenerateHtmlAsync(MeetingMinuteHtmlGenerateRequest request)
        {
            try
            {
                var html = await GenerateHtmlAsync(request.meetingContentUuid, request.language, request.includeActions);

                var contentRepository = new Atlas.Data.Repository.ContentRepository(_authUtil.UserId);
                var meetingContent = await contentRepository.GetSimpleContent(request.meetingContentUuid, includeRelatedEntities: true, isMeeting: true);

                var response = new MeetingMinuteHtmlGenerateResponse
                {
                    html = html,
                    meetingContentUuid = request.meetingContentUuid,
                    meetingTitle = meetingContent?.title ?? "",
                    meetingDate = meetingContent?.Meeting?.FirstOrDefault()?.date ?? DateTime.UtcNow,
                    workgroupName = meetingContent?.Workgroup?.name ?? "",
                    language = request.language,
                    actionsIncluded = request.includeActions
                };

                return ApiResponse<MeetingMinuteHtmlGenerateResponse>.CreateSuccess(response);
            }
            catch (Exception ex)
            {
                _logger.LogError("Error generating meeting minute HTML response.", ex);
                throw new HttpCustomException("Error generating meeting minute HTML.", HttpStatusCode.InternalServerError);
            }
        }
    }
}
